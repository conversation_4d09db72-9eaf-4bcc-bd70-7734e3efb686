// 路径规划功能组合式函数

import { ref, reactive, computed, readonly } from 'vue';
import { HuaweiRoutingService } from '../services/routing-service';
import type {
  LatLng,
  RouteType,
  DrivingOptions,
  TransitOptions,
  RouteOptions,
  RouteResult,
  TransitRouteResult,
  RouteDisplayOptions
} from '../types/routing';

/**
 * 路径规划功能组合式函数
 */
export function useRouting(apiKey?: string, map?: any) {
  // 路径规划服务实例
  const routingService = ref<HuaweiRoutingService | null>(null);

  // 路径规划状态
  const isPlanning = ref(false);
  const routingError = ref<string | null>(null);

  // 路径结果
  const currentRoute = ref<RouteResult | null>(null);
  const routeHistory = ref<RouteResult[]>([]);
  const maxHistorySize = 5;

  // 路径显示状态
  const isRouteDisplayed = ref(false);
  const displayOptions = ref<RouteDisplayOptions>({
    strokeColor: '#1890ff',
    strokeWeight: 6,
    strokeOpacity: 0.8,
    showDirections: true,
    showMarkers: true,
    startIcon: 'start',
    endIcon: 'end'
  });

  // 路径统计
  const routingStats = reactive({
    totalRoutes: 0,
    successfulRoutes: 0,
    failedRoutes: 0,
    totalDistance: 0,
    totalDuration: 0
  });

  /**
   * 初始化路径规划服务
   */
  const initRoutingService = (key: string, mapInstance?: any) => {
    routingService.value = new HuaweiRoutingService(key, mapInstance);
  };

  /**
   * 设置地图实例
   */
  const setMap = (mapInstance: any) => {
    if (routingService.value) {
      routingService.value.setMap(mapInstance);
    }
  };

  /**
   * 驾车路径规划
   */
  const planDrivingRoute = async (
    origin: LatLng,
    destination: LatLng,
    options: DrivingOptions = {}
  ): Promise<RouteResult> => {
    return await planRoute('driving', origin, destination, { drivingOptions: options });
  };

  /**
   * 步行路径规划
   */
  const planWalkingRoute = async (origin: LatLng, destination: LatLng): Promise<RouteResult> => {
    return await planRoute('walking', origin, destination);
  };

  /**
   * 骑行路径规划
   */
  const planBicyclingRoute = async (origin: LatLng, destination: LatLng): Promise<RouteResult> => {
    return await planRoute('bicycling', origin, destination);
  };

  /**
   * 公交路径规划
   */
  const planTransitRoute = async (
    origin: LatLng,
    destination: LatLng,
    options: TransitOptions = {}
  ): Promise<TransitRouteResult> => {
    return await planRoute('transit', origin, destination, { transitOptions: options }) as TransitRouteResult;
  };

  /**
   * 多点路径规划
   */
  const planMultiPointRoute = async (
    waypoints: LatLng[],
    options: RouteOptions = { type: 'driving' }
  ): Promise<RouteResult> => {
    if (!routingService.value) {
      if (!apiKey) {
        throw new Error('路径规划服务未初始化，请提供API密钥');
      }
      initRoutingService(apiKey, map);
    }

    if (waypoints.length < 2) {
      throw new Error('至少需要2个路径点');
    }

    isPlanning.value = true;
    routingError.value = null;
    routingStats.totalRoutes++;

    try {
      const result = await routingService.value!.planMultiPointRoute(waypoints, options);

      // 更新当前路径
      currentRoute.value = result;

      // 添加到历史记录
      addToHistory(result);

      // 更新统计
      routingStats.successfulRoutes++;
      routingStats.totalDistance += result.totalDistance;
      routingStats.totalDuration += result.totalDuration;

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '多点路径规划失败';
      routingError.value = errorMessage;
      routingStats.failedRoutes++;
      throw error;
    } finally {
      isPlanning.value = false;
    }
  };

  /**
   * 通用路径规划方法
   */
  const planRoute = async (
    type: RouteType,
    origin: LatLng,
    destination: LatLng,
    options: Partial<RouteOptions> = {}
  ): Promise<RouteResult> => {
    if (!routingService.value) {
      if (!apiKey) {
        throw new Error('路径规划服务未初始化，请提供API密钥');
      }
      initRoutingService(apiKey, map);
    }

    isPlanning.value = true;
    routingError.value = null;
    routingStats.totalRoutes++;

    try {
      let result: RouteResult;

      switch (type) {
        case 'driving':
          result = await routingService.value!.planDrivingRoute(origin, destination, options.drivingOptions);
          break;
        case 'walking':
          result = await routingService.value!.planWalkingRoute(origin, destination);
          break;
        case 'bicycling':
          result = await routingService.value!.planBicyclingRoute(origin, destination);
          break;
        case 'transit':
          result = await routingService.value!.planTransitRoute(origin, destination, options.transitOptions);
          break;
        default:
          throw new Error(`不支持的路径类型: ${type}`);
      }

      // 更新当前路径
      currentRoute.value = result;

      // 添加到历史记录
      addToHistory(result);

      // 更新统计
      routingStats.successfulRoutes++;
      routingStats.totalDistance += result.totalDistance;
      routingStats.totalDuration += result.totalDuration;

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '路径规划失败';
      routingError.value = errorMessage;
      routingStats.failedRoutes++;
      throw error;
    } finally {
      isPlanning.value = false;
    }
  };

  /**
   * 显示路径
   */
  const displayRoute = (route?: RouteResult, options?: RouteDisplayOptions) => {
    if (!routingService.value) {
      console.warn('路径规划服务未初始化');
      return;
    }

    const routeToDisplay = route || currentRoute.value;
    if (!routeToDisplay) {
      console.warn('没有可显示的路径');
      return;
    }

    const mergedOptions = { ...displayOptions.value, ...options };
    routingService.value.displayRoute(routeToDisplay, mergedOptions);
    isRouteDisplayed.value = true;
  };

  /**
   * 清除路径显示
   */
  const clearRoute = () => {
    if (routingService.value) {
      routingService.value.clearRoute();
      isRouteDisplayed.value = false;
    }
  };

  /**
   * 清除所有路径
   */
  const clearAllRoutes = () => {
    if (routingService.value) {
      routingService.value.clearAllRoutes();
      isRouteDisplayed.value = false;
    }
  };

  /**
   * 设置路径显示选项
   */
  const setDisplayOptions = (options: Partial<RouteDisplayOptions>) => {
    displayOptions.value = { ...displayOptions.value, ...options };
  };

  /**
   * 添加到路径历史
   */
  const addToHistory = (route: RouteResult) => {
    // 移除重复的路径（基于ID）
    const index = routeHistory.value.findIndex(r => r.id === route.id);
    if (index > -1) {
      routeHistory.value.splice(index, 1);
    }

    // 添加到开头
    routeHistory.value.unshift(route);

    // 限制历史记录数量
    if (routeHistory.value.length > maxHistorySize) {
      routeHistory.value = routeHistory.value.slice(0, maxHistorySize);
    }
  };

  /**
   * 清除路径历史
   */
  const clearHistory = () => {
    routeHistory.value = [];
  };

  /**
   * 重置路径规划状态
   */
  const resetRouting = () => {
    currentRoute.value = null;
    clearHistory();
    clearAllRoutes();
    routingError.value = null;
    routingStats.totalRoutes = 0;
    routingStats.successfulRoutes = 0;
    routingStats.failedRoutes = 0;
    routingStats.totalDistance = 0;
    routingStats.totalDuration = 0;
  };

  /**
   * 格式化距离
   */
  const formatDistance = (distance: number): string => {
    if (distance < 1000) {
      return `${Math.round(distance)}米`;
    } else {
      return `${(distance / 1000).toFixed(1)}公里`;
    }
  };

  /**
   * 格式化时间
   */
  const formatDuration = (duration: number): string => {
    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor((duration % 3600) / 60);

    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  };

  // 计算属性
  const hasRoute = computed(() => !!currentRoute.value);
  const hasError = computed(() => !!routingError.value);
  const successRate = computed(() => {
    if (routingStats.totalRoutes === 0) return 0;
    return (routingStats.successfulRoutes / routingStats.totalRoutes) * 100;
  });
  const averageDistance = computed(() => {
    if (routingStats.successfulRoutes === 0) return 0;
    return routingStats.totalDistance / routingStats.successfulRoutes;
  });
  const averageDuration = computed(() => {
    if (routingStats.successfulRoutes === 0) return 0;
    return routingStats.totalDuration / routingStats.successfulRoutes;
  });

  // 如果提供了API密钥，立即初始化
  if (apiKey) {
    initRoutingService(apiKey, map);
  }

  return {
    // 状态
    isPlanning: readonly(isPlanning),
    routingError: readonly(routingError),
    isRouteDisplayed: readonly(isRouteDisplayed),

    // 结果
    currentRoute: readonly(currentRoute),
    routeHistory: readonly(routeHistory),
    displayOptions: readonly(displayOptions),
    routingStats: readonly(routingStats),

    // 计算属性
    hasRoute,
    hasError,
    successRate,
    averageDistance,
    averageDuration,

    // 方法
    initRoutingService,
    setMap,
    planDrivingRoute,
    planWalkingRoute,
    planBicyclingRoute,
    planTransitRoute,
    planMultiPointRoute,
    displayRoute,
    clearRoute,
    clearAllRoutes,
    setDisplayOptions,
    clearHistory,
    resetRouting,
    formatDistance,
    formatDuration
  };
}
