// 搜索功能组合式函数

import { ref, reactive, computed, readonly } from 'vue';
import { HuaweiSearchService } from '../services/search-service';
import type {
  LatLng,
  SearchOptions,
  SearchResult,
  GeocodingResult,
  ReverseGeocodingResult,
  POIResult,
  SuggestionResult
} from '../types/search';

/**
 * 搜索功能组合式函数
 */
export function useSearch(apiKey?: string) {
  // 搜索服务实例
  const searchService = ref<HuaweiSearchService | null>(null);

  // 搜索状态
  const isSearching = ref(false);
  const searchError = ref<string | null>(null);

  // 搜索结果
  const searchResults = ref<SearchResult[]>([]);
  const geocodingResult = ref<GeocodingResult | null>(null);
  const reverseGeocodingResult = ref<ReverseGeocodingResult | null>(null);
  const nearbyResults = ref<POIResult[]>([]);
  const suggestions = ref<SuggestionResult[]>([]);

  // 搜索历史
  const searchHistory = ref<string[]>([]);
  const maxHistorySize = 10;

  // 搜索统计
  const searchStats = reactive({
    totalSearches: 0,
    successfulSearches: 0,
    failedSearches: 0
  });

  /**
   * 初始化搜索服务
   */
  const initSearchService = (key: string) => {
    searchService.value = new HuaweiSearchService(key);
  };

  /**
   * 地点搜索
   */
  const searchPlace = async (query: string, options: SearchOptions = {}): Promise<SearchResult[]> => {
    if (!searchService.value) {
      if (!apiKey) {
        throw new Error('搜索服务未初始化，请提供API密钥');
      }
      initSearchService(apiKey);
    }

    isSearching.value = true;
    searchError.value = null;
    searchStats.totalSearches++;

    try {
      const results = await searchService.value!.searchPlace(query, options);
      searchResults.value = results;
      searchStats.successfulSearches++;

      // 添加到搜索历史
      addToHistory(query);

      return results;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '搜索失败';
      searchError.value = errorMessage;
      searchStats.failedSearches++;
      throw error;
    } finally {
      isSearching.value = false;
    }
  };

  /**
   * 地理编码
   */
  const geocode = async (address: string): Promise<GeocodingResult> => {
    if (!searchService.value) {
      if (!apiKey) {
        throw new Error('搜索服务未初始化，请提供API密钥');
      }
      initSearchService(apiKey);
    }

    isSearching.value = true;
    searchError.value = null;

    try {
      const result = await searchService.value!.geocode(address);
      geocodingResult.value = result;
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '地理编码失败';
      searchError.value = errorMessage;
      throw error;
    } finally {
      isSearching.value = false;
    }
  };

  /**
   * 逆地理编码
   */
  const reverseGeocode = async (lat: number, lng: number): Promise<ReverseGeocodingResult> => {
    if (!searchService.value) {
      if (!apiKey) {
        throw new Error('搜索服务未初始化，请提供API密钥');
      }
      initSearchService(apiKey);
    }

    isSearching.value = true;
    searchError.value = null;

    try {
      const result = await searchService.value!.reverseGeocode(lat, lng);
      reverseGeocodingResult.value = result;
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '逆地理编码失败';
      searchError.value = errorMessage;
      throw error;
    } finally {
      isSearching.value = false;
    }
  };

  /**
   * 周边搜索
   */
  const searchNearby = async (center: LatLng, radius: number, keyword?: string): Promise<POIResult[]> => {
    if (!searchService.value) {
      if (!apiKey) {
        throw new Error('搜索服务未初始化，请提供API密钥');
      }
      initSearchService(apiKey);
    }

    isSearching.value = true;
    searchError.value = null;

    try {
      const results = await searchService.value!.searchNearby(center, radius, keyword);
      nearbyResults.value = results;
      return results;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '周边搜索失败';
      searchError.value = errorMessage;
      throw error;
    } finally {
      isSearching.value = false;
    }
  };

  /**
   * 获取搜索建议
   */
  const getSuggestions = async (query: string): Promise<SuggestionResult[]> => {
    if (!searchService.value) {
      if (!apiKey) {
        throw new Error('搜索服务未初始化，请提供API密钥');
      }
      initSearchService(apiKey);
    }

    if (!query.trim()) {
      suggestions.value = [];
      return [];
    }

    try {
      const results = await searchService.value!.getSuggestions(query);
      suggestions.value = results;
      return results;
    } catch (error) {
      console.warn('获取搜索建议失败:', error);
      suggestions.value = [];
      return [];
    }
  };

  /**
   * 添加到搜索历史
   */
  const addToHistory = (query: string) => {
    const trimmedQuery = query.trim();
    if (!trimmedQuery) return;

    // 移除重复项
    const index = searchHistory.value.indexOf(trimmedQuery);
    if (index > -1) {
      searchHistory.value.splice(index, 1);
    }

    // 添加到开头
    searchHistory.value.unshift(trimmedQuery);

    // 限制历史记录数量
    if (searchHistory.value.length > maxHistorySize) {
      searchHistory.value = searchHistory.value.slice(0, maxHistorySize);
    }
  };

  /**
   * 清除搜索历史
   */
  const clearHistory = () => {
    searchHistory.value = [];
  };

  /**
   * 清除搜索结果
   */
  const clearResults = () => {
    searchResults.value = [];
    geocodingResult.value = null;
    reverseGeocodingResult.value = null;
    nearbyResults.value = [];
    suggestions.value = [];
    searchError.value = null;
  };

  /**
   * 重置搜索状态
   */
  const resetSearch = () => {
    clearResults();
    clearHistory();
    searchStats.totalSearches = 0;
    searchStats.successfulSearches = 0;
    searchStats.failedSearches = 0;
  };

  // 计算属性
  const hasResults = computed(() => searchResults.value.length > 0);
  const hasError = computed(() => !!searchError.value);
  const successRate = computed(() => {
    if (searchStats.totalSearches === 0) return 0;
    return (searchStats.successfulSearches / searchStats.totalSearches) * 100;
  });

  // 如果提供了API密钥，立即初始化
  if (apiKey) {
    initSearchService(apiKey);
  }

  return {
    // 状态
    isSearching: readonly(isSearching),
    searchError: readonly(searchError),

    // 结果
    searchResults: readonly(searchResults),
    geocodingResult: readonly(geocodingResult),
    reverseGeocodingResult: readonly(reverseGeocodingResult),
    nearbyResults: readonly(nearbyResults),
    suggestions: readonly(suggestions),

    // 历史和统计
    searchHistory: readonly(searchHistory),
    searchStats: readonly(searchStats),

    // 计算属性
    hasResults,
    hasError,
    successRate,

    // 方法
    initSearchService,
    searchPlace,
    geocode,
    reverseGeocode,
    searchNearby,
    getSuggestions,
    addToHistory,
    clearHistory,
    clearResults,
    resetSearch
  };
}
