// 华为地图路径规划服务实现

import type {
  LatLng,
  RouteType,
  DrivingOptions,
  TransitOptions,
  RouteOptions,
  RouteResult,
  TransitRouteResult,
  RouteDisplayOptions,
  RoutingService,
  RouteLeg,
  RouteStep,
  TransitDetail,
  TransitStop
} from '../types/routing';

/**
 * 华为地图路径规划服务
 */
export class HuaweiRoutingService implements RoutingService {
  private apiKey: string;
  private baseUrl: string;
  private map: any;
  private routePolylines: any[] = [];
  private routeMarkers: any[] = [];

  constructor(apiKey: string, map?: any) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://mapapi.cloud.huawei.com';
    this.map = map;
  }

  /**
   * 设置地图实例
   */
  setMap(map: any): void {
    this.map = map;
  }

  /**
   * 驾车路径规划
   */
  async planDrivingRoute(
    origin: LatLng,
    destination: LatLng,
    options: DrivingOptions = {}
  ): Promise<RouteResult> {
    try {
      const params = new URLSearchParams({
        key: this.apiKey,
        origin: `${origin.lat},${origin.lng}`,
        destination: `${destination.lat},${destination.lng}`,
        language: 'zh-CN'
      });

      // 添加驾车选项
      if (options.strategy) {
        const strategyMap = {
          fastest: '0',
          shortest: '1',
          avoid_traffic: '2',
          avoid_highway: '3'
        };
        params.append('strategy', strategyMap[options.strategy] || '0');
      }

      if (options.waypoints && options.waypoints.length > 0) {
        const waypoints = options.waypoints
          .map(wp => `${wp.lat},${wp.lng}`)
          .join('|');
        params.append('waypoints', waypoints);
      }

      const response = await fetch(`${this.baseUrl}/mapApi/v1/routeService/driving?${params}`);

      if (!response.ok) {
        throw new Error(`驾车路径规划请求失败: ${response.status}`);
      }

      const data = await response.json();

      if (data.returnCode !== '0') {
        throw new Error(`驾车路径规划失败: ${data.returnDesc}`);
      }

      return this.parseRouteResult(data.routes?.[0], 'driving');
    } catch (error) {
      console.error('驾车路径规划失败:', error);
      throw error;
    }
  }

  /**
   * 步行路径规划
   */
  async planWalkingRoute(origin: LatLng, destination: LatLng): Promise<RouteResult> {
    try {
      const params = new URLSearchParams({
        key: this.apiKey,
        origin: `${origin.lat},${origin.lng}`,
        destination: `${destination.lat},${destination.lng}`,
        language: 'zh-CN'
      });

      const response = await fetch(`${this.baseUrl}/mapApi/v1/routeService/walking?${params}`);

      if (!response.ok) {
        throw new Error(`步行路径规划请求失败: ${response.status}`);
      }

      const data = await response.json();

      if (data.returnCode !== '0') {
        throw new Error(`步行路径规划失败: ${data.returnDesc}`);
      }

      return this.parseRouteResult(data.routes?.[0], 'walking');
    } catch (error) {
      console.error('步行路径规划失败:', error);
      throw error;
    }
  }

  /**
   * 骑行路径规划
   */
  async planBicyclingRoute(origin: LatLng, destination: LatLng): Promise<RouteResult> {
    try {
      const params = new URLSearchParams({
        key: this.apiKey,
        origin: `${origin.lat},${origin.lng}`,
        destination: `${destination.lat},${destination.lng}`,
        language: 'zh-CN'
      });

      const response = await fetch(`${this.baseUrl}/mapApi/v1/routeService/bicycling?${params}`);

      if (!response.ok) {
        throw new Error(`骑行路径规划请求失败: ${response.status}`);
      }

      const data = await response.json();

      if (data.returnCode !== '0') {
        throw new Error(`骑行路径规划失败: ${data.returnDesc}`);
      }

      return this.parseRouteResult(data.routes?.[0], 'bicycling');
    } catch (error) {
      console.error('骑行路径规划失败:', error);
      throw error;
    }
  }

  /**
   * 公交路径规划
   */
  async planTransitRoute(
    origin: LatLng,
    destination: LatLng,
    options: TransitOptions = {}
  ): Promise<TransitRouteResult> {
    try {
      const params = new URLSearchParams({
        key: this.apiKey,
        origin: `${origin.lat},${origin.lng}`,
        destination: `${destination.lat},${destination.lng}`,
        language: 'zh-CN'
      });

      if (options.departureTime) {
        params.append('departure_time', Math.floor(options.departureTime.getTime() / 1000).toString());
      }

      if (options.modes && options.modes.length > 0) {
        params.append('mode', options.modes.join('|'));
      }

      const response = await fetch(`${this.baseUrl}/mapApi/v1/routeService/transit?${params}`);

      if (!response.ok) {
        throw new Error(`公交路径规划请求失败: ${response.status}`);
      }

      const data = await response.json();

      if (data.returnCode !== '0') {
        throw new Error(`公交路径规划失败: ${data.returnDesc}`);
      }

      const route = this.parseRouteResult(data.routes?.[0], 'transit') as TransitRouteResult;
      route.transitDetails = this.parseTransitDetails(data.routes?.[0]?.legs || []);

      return route;
    } catch (error) {
      console.error('公交路径规划失败:', error);
      throw error;
    }
  }

  /**
   * 多点路径规划
   */
  async planMultiPointRoute(waypoints: LatLng[], options: RouteOptions = { type: 'driving' }): Promise<RouteResult> {
    if (waypoints.length < 2) {
      throw new Error('至少需要2个路径点');
    }

    const origin = waypoints[0];
    const destination = waypoints[waypoints.length - 1];
    const intermediateWaypoints = waypoints.slice(1, -1);

    switch (options.type) {
      case 'driving':
        return this.planDrivingRoute(origin, destination, {
          ...options.drivingOptions,
          waypoints: intermediateWaypoints
        });
      case 'walking':
        return this.planWalkingRoute(origin, destination);
      case 'bicycling':
        return this.planBicyclingRoute(origin, destination);
      case 'transit':
        return this.planTransitRoute(origin, destination, options.transitOptions);
      default:
        throw new Error(`不支持的路径类型: ${options.type}`);
    }
  }

  /**
   * 在地图上显示路径
   */
  displayRoute(route: RouteResult, options: RouteDisplayOptions = {}): void {
    if (!this.map) {
      console.warn('地图实例未设置，无法显示路径');
      return;
    }

    // 清除之前的路径
    this.clearRoute();

    const defaultOptions = {
      strokeColor: '#1890ff',
      strokeWeight: 6,
      strokeOpacity: 0.8,
      showDirections: true,
      showMarkers: true,
      startIcon: 'start',
      endIcon: 'end'
    };

    const displayOptions = { ...defaultOptions, ...options };

    try {
      // 创建路径折线
      if (window.HWMapJsSDK && (window.HWMapJsSDK as any).HWPolyline) {
        const polyline = new (window.HWMapJsSDK as any).HWPolyline({
          path: route.polyline,
          strokeColor: displayOptions.strokeColor,
          strokeWeight: displayOptions.strokeWeight,
          strokeOpacity: displayOptions.strokeOpacity
        });

        polyline.setMap(this.map);
        this.routePolylines.push(polyline);
      }

      // 添加起终点标记
      if (displayOptions.showMarkers && window.HWMapJsSDK && (window.HWMapJsSDK as any).HWMarker) {
        const startMarker = new (window.HWMapJsSDK as any).HWMarker({
          position: route.polyline[0],
          icon: displayOptions.startIcon
        });
        startMarker.setMap(this.map);
        this.routeMarkers.push(startMarker);

        const endMarker = new (window.HWMapJsSDK as any).HWMarker({
          position: route.polyline[route.polyline.length - 1],
          icon: displayOptions.endIcon
        });
        endMarker.setMap(this.map);
        this.routeMarkers.push(endMarker);
      }

      // 调整地图视野以包含整个路径
      if (this.map.fitBounds) {
        this.map.fitBounds(route.bounds);
      }
    } catch (error) {
      console.error('显示路径失败:', error);
    }
  }

  /**
   * 清除路径显示
   */
  clearRoute(): void {
    // 清除路径线
    this.routePolylines.forEach(polyline => {
      if (polyline.setMap) {
        polyline.setMap(null);
      }
    });
    this.routePolylines = [];

    // 清除标记
    this.routeMarkers.forEach(marker => {
      if (marker.setMap) {
        marker.setMap(null);
      }
    });
    this.routeMarkers = [];
  }

  /**
   * 清除所有路径
   */
  clearAllRoutes(): void {
    this.clearRoute();
  }

  /**
   * 解析路径结果
   */
  private parseRouteResult(route: any, type: RouteType): RouteResult {
    if (!route) {
      throw new Error('无效的路径数据');
    }

    const legs = this.parseRouteLegs(route.legs || []);
    const polyline = this.decodePolyline(route.overview_polyline?.points || '');

    return {
      id: route.id || Math.random().toString(36),
      type,
      totalDistance: route.distance?.value || 0,
      totalDuration: route.duration?.value || 0,
      legs,
      polyline,
      bounds: {
        northeast: route.bounds?.northeast || polyline[0],
        southwest: route.bounds?.southwest || polyline[0]
      },
      summary: route.summary || '',
      fare: route.fare ? {
        currency: route.fare.currency || 'CNY',
        value: route.fare.value || 0
      } : undefined
    };
  }

  /**
   * 解析路径段
   */
  private parseRouteLegs(legs: any[]): RouteLeg[] {
    return legs.map(leg => ({
      startLocation: {
        lat: leg.start_location?.lat || 0,
        lng: leg.start_location?.lng || 0
      },
      endLocation: {
        lat: leg.end_location?.lat || 0,
        lng: leg.end_location?.lng || 0
      },
      startAddress: leg.start_address || '',
      endAddress: leg.end_address || '',
      distance: leg.distance?.value || 0,
      duration: leg.duration?.value || 0,
      steps: this.parseRouteSteps(leg.steps || [])
    }));
  }

  /**
   * 解析路径步骤
   */
  private parseRouteSteps(steps: any[]): RouteStep[] {
    return steps.map(step => ({
      instruction: step.html_instructions || step.instructions || '',
      distance: step.distance?.value || 0,
      duration: step.duration?.value || 0,
      polyline: this.decodePolyline(step.polyline?.points || ''),
      maneuver: step.maneuver || '',
      roadName: step.road_name || ''
    }));
  }

  /**
   * 解析公交详情
   */
  private parseTransitDetails(legs: any[]): TransitDetail[] {
    const details: TransitDetail[] = [];

    legs.forEach(leg => {
      leg.steps?.forEach((step: any) => {
        if (step.travel_mode === 'TRANSIT' && step.transit_details) {
          const transit = step.transit_details;
          details.push({
            lineName: transit.line?.name || '',
            mode: this.mapTransitMode(transit.line?.vehicle?.type),
            departureStop: this.parseTransitStop(transit.departure_stop),
            arrivalStop: this.parseTransitStop(transit.arrival_stop),
            numStops: transit.num_stops || 0,
            color: transit.line?.color || undefined
          });
        }
      });
    });

    return details;
  }

  /**
   * 解析公交站点
   */
  private parseTransitStop(stop: any): TransitStop {
    return {
      name: stop?.name || '',
      location: {
        lat: stop?.location?.lat || 0,
        lng: stop?.location?.lng || 0
      },
      arrivalTime: stop?.arrival_time?.value ? new Date(stop.arrival_time.value * 1000) : undefined,
      departureTime: stop?.departure_time?.value ? new Date(stop.departure_time.value * 1000) : undefined
    };
  }

  /**
   * 映射公交方式
   */
  private mapTransitMode(vehicleType: string): 'bus' | 'subway' | 'train' | 'tram' {
    const modeMap: Record<string, 'bus' | 'subway' | 'train' | 'tram'> = {
      'BUS': 'bus',
      'SUBWAY': 'subway',
      'TRAIN': 'train',
      'TRAM': 'tram'
    };
    return modeMap[vehicleType] || 'bus';
  }

  /**
   * 解码polyline字符串为坐标数组
   */
  private decodePolyline(encoded: string): LatLng[] {
    if (!encoded) return [];

    const points: LatLng[] = [];
    let index = 0;
    let lat = 0;
    let lng = 0;

    while (index < encoded.length) {
      let shift = 0;
      let result = 0;
      let byte: number;

      do {
        byte = encoded.charCodeAt(index++) - 63;
        result |= (byte & 0x1f) << shift;
        shift += 5;
      } while (byte >= 0x20);

      const deltaLat = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lat += deltaLat;

      shift = 0;
      result = 0;

      do {
        byte = encoded.charCodeAt(index++) - 63;
        result |= (byte & 0x1f) << shift;
        shift += 5;
      } while (byte >= 0x20);

      const deltaLng = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lng += deltaLng;

      points.push({
        lat: lat / 1e5,
        lng: lng / 1e5
      });
    }

    return points;
  }
}
