<template>
  <div class="advanced-test">
    <h1>华为地图Vue3组件 - 高级功能测试 (v1.1.0)</h1>

    <div class="test-layout">
      <!-- 左侧功能面板 -->
      <div class="panels-container">
        <!-- 搜索面板 -->
        <SearchPanel
          :api-key="apiKey"
          :map-center="mapCenter"
          @result-selected="onSearchResultSelected"
          @show-on-map="onShowOnMap"
          @start-routing="onStartRouting"
          @geocode-result="onGeocodeResult"
        />

        <!-- 路径规划面板 -->
        <RoutingPanel
          :api-key="apiKey"
          :map="map"
          @route-planned="onRoutePlanned"
          @route-displayed="onRouteDisplayed"
          @route-cleared="onRouteCleared"
          @waypoint-selected="onWaypointSelected"
        />
      </div>

      <!-- 右侧地图区域 -->
      <div class="map-container">
        <HuaweiMap
          ref="mapRef"
          :api-key="apiKey"
          width="100%"
          height="100%"
          :center="mapCenter"
          :zoom="mapZoom"
          @map-ready="onMapReady"
          @map-click="onMapClick"
        />

        <!-- 地图控制按钮 -->
        <div class="map-controls">
          <button @click="clearAllMarkers" class="control-btn">清除标记</button>
          <button @click="clearAllRoutes" class="control-btn">清除路径</button>
          <button @click="resetMap" class="control-btn">重置地图</button>
        </div>

        <!-- 状态显示 -->
        <div class="status-panel">
          <div class="status-item">
            <span class="status-label">地图中心:</span>
            <span class="status-value">{{ mapCenter.lat.toFixed(4) }}, {{ mapCenter.lng.toFixed(4) }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">缩放级别:</span>
            <span class="status-value">{{ mapZoom }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">标记数量:</span>
            <span class="status-value">{{ markers.length }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">当前模式:</span>
            <span class="status-value">{{ currentMode }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 事件日志 -->
    <div class="event-log">
      <h3>事件日志</h3>
      <div class="log-content">
        <div
          v-for="(event, index) in eventLog"
          :key="index"
          class="log-item"
          :class="event.type"
        >
          <span class="log-time">{{ event.time }}</span>
          <span class="log-message">{{ event.message }}</span>
        </div>
      </div>
      <button @click="clearLog" class="clear-log-btn">清除日志</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import HuaweiMap from '../components/HuaweiMap.vue'
import SearchPanel from '../components/SearchPanel.vue'
import RoutingPanel from '../components/RoutingPanel.vue'
import { useMapMarkers } from '../composables/useMapMarkers'
import { useRouting } from '../composables/useRouting'
import type { SearchResult } from '../types/search'
import type { LatLng } from '../types/routing'

// API密钥
const apiKey = ref('')

// 地图相关
const mapRef = ref()
const map = ref<any>(null)
const mapCenter = ref<LatLng>({ lat: 39.9042, lng: 116.4074 })
const mapZoom = ref(10)

// 当前模式
const currentMode = ref('浏览')

// 标记管理
const { markers, addMarker, removeMarker, clearMarkers } = useMapMarkers(map)

// 路径规划
const { clearAllRoutes } = useRouting(apiKey.value)

// 事件日志
const eventLog = ref<Array<{ time: string; message: string; type: string }>>([])

// 加载API密钥
onMounted(async () => {
  try {
    const response = await fetch('/test.key')
    apiKey.value = await response.text()
    addEvent('info', 'API密钥加载成功')
  } catch (error) {
    console.error('加载API密钥失败:', error)
    addEvent('error', 'API密钥加载失败')
  }
})

// 事件处理
const onMapReady = (mapInstance: any) => {
  map.value = mapInstance
  addEvent('success', '地图初始化完成')
}

const onMapClick = (event: any) => {
  const { lat, lng } = event.latLng
  addEvent('info', `地图点击: ${lat.toFixed(4)}, ${lng.toFixed(4)}`)

  if (currentMode.value === '选择起点' || currentMode.value === '选择终点') {
    // 路径规划模式下的点击处理
    handleWaypointSelection(lat, lng)
  }
}

const onSearchResultSelected = (result: SearchResult) => {
  addEvent('success', `选择搜索结果: ${result.name}`)

  // 在地图上添加标记
  addMarker({
    id: `search-${result.id}`,
    position: result.location,
    title: result.name,
    content: `
      <div>
        <h4>${result.name}</h4>
        <p>${result.address}</p>
        ${result.phone ? `<p>电话: ${result.phone}</p>` : ''}
        ${result.rating ? `<p>评分: ${result.rating}</p>` : ''}
      </div>
    `
  })

  // 移动地图中心到搜索结果
  mapCenter.value = result.location
  mapZoom.value = 15
}

const onShowOnMap = (result: SearchResult) => {
  addEvent('info', `在地图上显示: ${result.name}`)

  // 移动地图中心并添加标记
  mapCenter.value = result.location
  mapZoom.value = 16

  addMarker({
    id: `show-${result.id}`,
    position: result.location,
    title: result.name,
    content: `<div><h4>${result.name}</h4><p>${result.address}</p></div>`
  })
}

const onStartRouting = (result: SearchResult) => {
  addEvent('info', `开始路径规划到: ${result.name}`)
  currentMode.value = '选择起点'

  // 这里可以自动设置终点为搜索结果
  // 实际实现中可以通过事件通信到RoutingPanel
}

const onGeocodeResult = (result: any) => {
  addEvent('success', `地理编码结果: ${result.location.lat}, ${result.location.lng}`)

  // 在地图上显示地理编码结果
  addMarker({
    id: `geocode-${Date.now()}`,
    position: result.location,
    title: '地理编码结果',
    content: `
      <div>
        <h4>地理编码结果</h4>
        <p>地址: ${result.address}</p>
        <p>坐标: ${result.location.lat}, ${result.location.lng}</p>
        <p>置信度: ${result.confidence}</p>
      </div>
    `
  })

  mapCenter.value = result.location
  mapZoom.value = 15
}

const onRoutePlanned = (route: any) => {
  addEvent('success', `路径规划完成: ${route.summary}`)
  addEvent('info', `距离: ${formatDistance(route.totalDistance)}, 时间: ${formatDuration(route.totalDuration)}`)
}

const onRouteDisplayed = (route: any) => {
  addEvent('info', `路径已显示在地图上`)
}

const onRouteCleared = () => {
  addEvent('info', '路径已清除')
}

const onWaypointSelected = (type: string) => {
  currentMode.value = type === 'origin' ? '选择起点' : '选择终点'
  addEvent('info', `请在地图上点击选择${type === 'origin' ? '起点' : '终点'}`)
}

const handleWaypointSelection = (lat: number, lng: number) => {
  const location = { lat, lng }

  if (currentMode.value === '选择起点') {
    addMarker({
      id: 'origin',
      position: location,
      title: '起点',
      content: '<div><h4>起点</h4></div>'
    })
    addEvent('success', `起点已设置: ${lat.toFixed(4)}, ${lng.toFixed(4)}`)
  } else if (currentMode.value === '选择终点') {
    addMarker({
      id: 'destination',
      position: location,
      title: '终点',
      content: '<div><h4>终点</h4></div>'
    })
    addEvent('success', `终点已设置: ${lat.toFixed(4)}, ${lng.toFixed(4)}`)
  }

  currentMode.value = '浏览'
}

const clearAllMarkers = () => {
  clearMarkers()
  addEvent('info', '所有标记已清除')
}

const resetMap = () => {
  mapCenter.value = { lat: 39.9042, lng: 116.4074 }
  mapZoom.value = 10
  clearMarkers()
  clearAllRoutes()
  currentMode.value = '浏览'
  addEvent('info', '地图已重置')
}

const addEvent = (type: string, message: string) => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  eventLog.value.unshift({ time, message, type })

  // 限制日志数量
  if (eventLog.value.length > 100) {
    eventLog.value = eventLog.value.slice(0, 100)
  }
}

const clearLog = () => {
  eventLog.value = []
}

const formatDistance = (distance: number): string => {
  if (distance < 1000) {
    return `${Math.round(distance)}米`
  } else {
    return `${(distance / 1000).toFixed(1)}公里`
  }
}

const formatDuration = (duration: number): string => {
  const hours = Math.floor(duration / 3600)
  const minutes = Math.floor((duration % 3600) / 60)

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}
</script>

<style scoped>
.advanced-test {
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.advanced-test h1 {
  margin-bottom: 20px;
  color: #1890ff;
  text-align: center;
}

.test-layout {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 20px;
  height: 600px;
  margin-bottom: 20px;
}

.panels-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.map-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-btn {
  padding: 8px 12px;
  background: white;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.control-btn:hover {
  background: #f0f0f0;
}

.status-panel {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(255, 255, 255, 0.9);
  padding: 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  min-width: 200px;
}

.status-label {
  color: #666;
}

.status-value {
  font-weight: 500;
}

.event-log {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.event-log h3 {
  margin: 0;
  padding: 12px 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  font-size: 16px;
  font-weight: 600;
}

.log-content {
  max-height: 200px;
  overflow-y: auto;
  padding: 8px;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 4px 8px;
  border-radius: 3px;
  margin-bottom: 2px;
  font-size: 12px;
}

.log-item.success {
  background: #f6ffed;
  color: #52c41a;
}

.log-item.error {
  background: #fff2f0;
  color: #ff4d4f;
}

.log-item.info {
  background: #f0f9ff;
  color: #1890ff;
}

.log-time {
  color: #666;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.clear-log-btn {
  width: 100%;
  padding: 8px;
  background: #f5f5f5;
  border: none;
  border-top: 1px solid #e0e0e0;
  cursor: pointer;
  font-size: 12px;
}

.clear-log-btn:hover {
  background: #e0e0e0;
}
</style>
