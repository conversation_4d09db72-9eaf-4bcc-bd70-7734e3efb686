<template>
  <div class="search-panel">
    <div class="search-header">
      <h3>地点搜索</h3>
      <button
        class="toggle-btn"
        @click="togglePanel"
        :class="{ active: isExpanded }"
      >
        {{ isExpanded ? '收起' : '展开' }}
      </button>
    </div>

    <div v-show="isExpanded" class="search-content">
      <!-- 搜索输入框 -->
      <div class="search-input-group">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索地点、地址或POI"
          class="search-input"
          @input="onSearchInput"
          @keyup.enter="performSearch"
          @focus="showSuggestions = true"
        />
        <button
          class="search-btn"
          @click="performSearch"
          :disabled="isSearching || !searchQuery.trim()"
        >
          {{ isSearching ? '搜索中...' : '搜索' }}
        </button>
      </div>

      <!-- 搜索建议 -->
      <div v-if="showSuggestions && suggestions.length > 0" class="suggestions">
        <div
          v-for="(suggestion, index) in suggestions"
          :key="index"
          class="suggestion-item"
          @click="selectSuggestion(suggestion)"
        >
          <div class="suggestion-text">{{ suggestion.text }}</div>
          <div class="suggestion-type">{{ getSuggestionTypeText(suggestion.type) }}</div>
        </div>
      </div>

      <!-- 搜索历史 -->
      <div v-if="searchHistory.length > 0 && !showSuggestions" class="search-history">
        <div class="history-header">
          <span>搜索历史</span>
          <button class="clear-history-btn" @click="clearSearchHistory">清除</button>
        </div>
        <div class="history-items">
          <div
            v-for="(item, index) in searchHistory"
            :key="index"
            class="history-item"
            @click="searchFromHistory(item)"
          >
            {{ item }}
          </div>
        </div>
      </div>

      <!-- 搜索选项 -->
      <div class="search-options">
        <div class="option-group">
          <label>搜索范围:</label>
          <select v-model="searchOptions.city">
            <option value="">全国</option>
            <option value="北京">北京</option>
            <option value="上海">上海</option>
            <option value="广州">广州</option>
            <option value="深圳">深圳</option>
          </select>
        </div>
        <div class="option-group">
          <label>搜索半径:</label>
          <select v-model="searchOptions.radius">
            <option :value="1000">1公里</option>
            <option :value="3000">3公里</option>
            <option :value="5000">5公里</option>
            <option :value="10000">10公里</option>
          </select>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="hasError" class="error-message">
        {{ searchError }}
      </div>

      <!-- 搜索结果 -->
      <div v-if="hasResults" class="search-results">
        <div class="results-header">
          <span>搜索结果 ({{ searchResults.length }})</span>
        </div>
        <div class="results-list">
          <div
            v-for="(result, index) in searchResults"
            :key="result.id"
            class="result-item"
            @click="selectResult(result)"
          >
            <div class="result-info">
              <div class="result-name">{{ result.name }}</div>
              <div class="result-address">{{ result.address }}</div>
              <div class="result-meta">
                <span v-if="result.distance" class="distance">
                  {{ formatDistance(result.distance) }}
                </span>
                <span v-if="result.rating" class="rating">
                  ⭐ {{ result.rating }}
                </span>
              </div>
            </div>
            <div class="result-actions">
              <button
                class="action-btn"
                @click.stop="showOnMap(result)"
                title="在地图上显示"
              >
                📍
              </button>
              <button
                class="action-btn"
                @click.stop="startRouting(result)"
                title="路径规划"
              >
                🧭
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 地理编码工具 -->
      <div class="geocoding-tools">
        <h4>地理编码工具</h4>
        <div class="tool-group">
          <input
            v-model="geocodeAddress"
            type="text"
            placeholder="输入地址进行地理编码"
            class="tool-input"
          />
          <button
            class="tool-btn"
            @click="performGeocode"
            :disabled="isSearching || !geocodeAddress.trim()"
          >
            地址转坐标
          </button>
        </div>
        <div v-if="geocodingResult" class="geocode-result">
          <div>坐标: {{ geocodingResult.location.lat }}, {{ geocodingResult.location.lng }}</div>
          <div>置信度: {{ geocodingResult.confidence }}</div>
        </div>
      </div>

      <!-- 搜索统计 -->
      <div class="search-stats">
        <div class="stat-item">
          <span class="stat-label">总搜索次数:</span>
          <span class="stat-value">{{ searchStats.totalSearches }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">成功率:</span>
          <span class="stat-value">{{ successRate.toFixed(1) }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { useSearch } from '../composables/useSearch'
import type { SearchResult, SuggestionResult, LatLng } from '../types/search'

// Props
interface Props {
  apiKey: string
  mapCenter?: LatLng
}

const props = withDefaults(defineProps<Props>(), {
  mapCenter: () => ({ lat: 39.9042, lng: 116.4074 })
})

// Emits
const emit = defineEmits<{
  'result-selected': [result: SearchResult]
  'show-on-map': [result: SearchResult]
  'start-routing': [result: SearchResult]
  'geocode-result': [result: any]
}>()

// 使用搜索功能
const {
  isSearching,
  searchError,
  searchResults,
  geocodingResult,
  suggestions,
  searchHistory,
  searchStats,
  hasResults,
  hasError,
  successRate,
  searchPlace,
  geocode,
  getSuggestions,
  clearHistory
} = useSearch(props.apiKey)

// 组件状态
const isExpanded = ref(true)
const searchQuery = ref('')
const geocodeAddress = ref('')
const showSuggestions = ref(false)

// 搜索选项
const searchOptions = reactive({
  city: '',
  radius: 5000
})

// 防抖定时器
let suggestionTimer: NodeJS.Timeout | null = null

// 方法
const togglePanel = () => {
  isExpanded.value = !isExpanded.value
}

const onSearchInput = () => {
  if (suggestionTimer) {
    clearTimeout(suggestionTimer)
  }

  suggestionTimer = setTimeout(() => {
    if (searchQuery.value.trim()) {
      getSuggestions(searchQuery.value)
    }
  }, 300)
}

const performSearch = async () => {
  if (!searchQuery.value.trim()) return

  showSuggestions.value = false

  try {
    const options = {
      center: props.mapCenter,
      radius: searchOptions.radius,
      city: searchOptions.city || undefined
    }

    await searchPlace(searchQuery.value, options)
  } catch (error) {
    console.error('搜索失败:', error)
  }
}

const selectSuggestion = (suggestion: SuggestionResult) => {
  searchQuery.value = suggestion.text
  showSuggestions.value = false
  nextTick(() => {
    performSearch()
  })
}

const searchFromHistory = (query: string) => {
  searchQuery.value = query
  performSearch()
}

const clearSearchHistory = () => {
  clearHistory()
}

const selectResult = (result: SearchResult) => {
  emit('result-selected', result)
}

const showOnMap = (result: SearchResult) => {
  emit('show-on-map', result)
}

const startRouting = (result: SearchResult) => {
  emit('start-routing', result)
}

const performGeocode = async () => {
  if (!geocodeAddress.value.trim()) return

  try {
    const result = await geocode(geocodeAddress.value)
    emit('geocode-result', result)
  } catch (error) {
    console.error('地理编码失败:', error)
  }
}

const getSuggestionTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'place': '地点',
    'address': '地址',
    'poi': 'POI'
  }
  return typeMap[type] || type
}

const formatDistance = (distance: number) => {
  if (distance < 1000) {
    return `${Math.round(distance)}m`
  } else {
    return `${(distance / 1000).toFixed(1)}km`
  }
}

// 监听点击外部关闭建议
watch(() => showSuggestions.value, (show) => {
  if (show) {
    nextTick(() => {
      document.addEventListener('click', hideSuggestions)
    })
  } else {
    document.removeEventListener('click', hideSuggestions)
  }
})

const hideSuggestions = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.search-input-group') && !target.closest('.suggestions')) {
    showSuggestions.value = false
  }
}
</script>

<style scoped>
.search-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.search-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.toggle-btn {
  padding: 4px 8px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 12px;
}

.toggle-btn:hover {
  background: #f0f0f0;
}

.toggle-btn.active {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.search-content {
  padding: 16px;
}

.search-input-group {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  position: relative;
}

.search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.search-btn {
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.search-btn:hover:not(:disabled) {
  background: #40a9ff;
}

.search-btn:disabled {
  background: #d0d0d0;
  cursor: not-allowed;
}

.suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 60px;
  background: white;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
}

.suggestion-item:hover {
  background: #f5f5f5;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-text {
  font-size: 14px;
  margin-bottom: 2px;
}

.suggestion-type {
  font-size: 12px;
  color: #666;
}

.search-history {
  margin-bottom: 16px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.history-header span {
  font-size: 14px;
  font-weight: 500;
}

.clear-history-btn {
  padding: 2px 6px;
  background: none;
  border: 1px solid #d0d0d0;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.clear-history-btn:hover {
  background: #f0f0f0;
}

.history-items {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.history-item {
  padding: 4px 8px;
  background: #f0f0f0;
  border-radius: 12px;
  cursor: pointer;
  font-size: 12px;
}

.history-item:hover {
  background: #e0e0e0;
}

.search-options {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.option-group {
  display: flex;
  align-items: center;
  gap: 6px;
}

.option-group label {
  font-size: 12px;
  color: #666;
}

.option-group select {
  padding: 4px 6px;
  border: 1px solid #d0d0d0;
  border-radius: 3px;
  font-size: 12px;
}

.error-message {
  padding: 8px 12px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  color: #ff4d4f;
  font-size: 14px;
  margin-bottom: 12px;
}

.search-results {
  margin-bottom: 16px;
}

.results-header {
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
}

.results-list {
  max-height: 300px;
  overflow-y: auto;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
}

.result-item:hover {
  background: #f9f9f9;
  border-color: #d0d0d0;
}

.result-info {
  flex: 1;
}

.result-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.result-address {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.result-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.distance {
  color: #1890ff;
}

.rating {
  color: #faad14;
}

.result-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  padding: 4px 6px;
  background: none;
  border: 1px solid #d0d0d0;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.action-btn:hover {
  background: #f0f0f0;
}

.geocoding-tools {
  margin-bottom: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.geocoding-tools h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
}

.tool-group {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.tool-input {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #d0d0d0;
  border-radius: 3px;
  font-size: 12px;
}

.tool-btn {
  padding: 6px 12px;
  background: #52c41a;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.tool-btn:hover:not(:disabled) {
  background: #73d13d;
}

.tool-btn:disabled {
  background: #d0d0d0;
  cursor: not-allowed;
}

.geocode-result {
  padding: 8px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 3px;
  font-size: 12px;
}

.search-stats {
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.stat-label {
  color: #666;
}

.stat-value {
  font-weight: 500;
}
</style>
