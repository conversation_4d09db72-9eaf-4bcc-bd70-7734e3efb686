// 路径规划相关类型定义

export type { LatLng } from './search';

// 路径规划类型
export type RouteType = 'driving' | 'walking' | 'bicycling' | 'transit';

// 驾车策略
export type DrivingStrategy = 'fastest' | 'shortest' | 'avoid_traffic' | 'avoid_highway';

// 驾车选项
export interface DrivingOptions {
  // 路径策略
  strategy?: DrivingStrategy;
  // 避开区域
  avoidAreas?: LatLng[][];
  // 途经点
  waypoints?: LatLng[];
  // 车辆类型
  vehicleType?: 'car' | 'truck' | 'motorcycle';
}

// 公交选项
export interface TransitOptions {
  // 出行时间
  departureTime?: Date;
  // 交通方式偏好
  modes?: ('bus' | 'subway' | 'train' | 'tram')[];
  // 是否包含步行
  includeWalking?: boolean;
}

// 路径选项
export interface RouteOptions {
  // 路径类型
  type: RouteType;
  // 特定选项
  drivingOptions?: DrivingOptions;
  transitOptions?: TransitOptions;
}

// 路径点
export interface RouteStep {
  // 步骤描述
  instruction: string;
  // 距离（米）
  distance: number;
  // 时间（秒）
  duration: number;
  // 路径坐标点
  polyline: LatLng[];
  // 转向类型
  maneuver?: string;
  // 道路名称
  roadName?: string;
}

// 路径段
export interface RouteLeg {
  // 起点
  startLocation: LatLng;
  // 终点
  endLocation: LatLng;
  // 起点地址
  startAddress: string;
  // 终点地址
  endAddress: string;
  // 距离（米）
  distance: number;
  // 时间（秒）
  duration: number;
  // 路径步骤
  steps: RouteStep[];
}

// 路径结果
export interface RouteResult {
  // 路径ID
  id: string;
  // 路径类型
  type: RouteType;
  // 总距离（米）
  totalDistance: number;
  // 总时间（秒）
  totalDuration: number;
  // 路径段
  legs: RouteLeg[];
  // 完整路径坐标
  polyline: LatLng[];
  // 边界框
  bounds: {
    northeast: LatLng;
    southwest: LatLng;
  };
  // 路径摘要
  summary: string;
  // 费用信息（如有）
  fare?: {
    currency: string;
    value: number;
  };
}

// 公交路径结果
export interface TransitRouteResult extends RouteResult {
  // 公交线路信息
  transitDetails: TransitDetail[];
}

// 公交详情
export interface TransitDetail {
  // 线路名称
  lineName: string;
  // 交通方式
  mode: 'bus' | 'subway' | 'train' | 'tram';
  // 上车站
  departureStop: TransitStop;
  // 下车站
  arrivalStop: TransitStop;
  // 站点数
  numStops: number;
  // 线路颜色
  color?: string;
}

// 公交站点
export interface TransitStop {
  // 站点名称
  name: string;
  // 站点位置
  location: LatLng;
  // 到达时间
  arrivalTime?: Date;
  // 出发时间
  departureTime?: Date;
}

// 路径显示选项
export interface RouteDisplayOptions {
  // 线条颜色
  strokeColor?: string;
  // 线条宽度
  strokeWeight?: number;
  // 线条透明度
  strokeOpacity?: number;
  // 是否显示方向箭头
  showDirections?: boolean;
  // 是否显示起终点标记
  showMarkers?: boolean;
  // 起点图标
  startIcon?: string;
  // 终点图标
  endIcon?: string;
}

// 路径规划服务接口
export interface RoutingService {
  // 驾车路径规划
  planDrivingRoute(
    origin: LatLng,
    destination: LatLng,
    options?: DrivingOptions
  ): Promise<RouteResult>;

  // 步行路径规划
  planWalkingRoute(
    origin: LatLng,
    destination: LatLng
  ): Promise<RouteResult>;

  // 骑行路径规划
  planBicyclingRoute(
    origin: LatLng,
    destination: LatLng
  ): Promise<RouteResult>;

  // 公交路径规划
  planTransitRoute(
    origin: LatLng,
    destination: LatLng,
    options?: TransitOptions
  ): Promise<TransitRouteResult>;

  // 多点路径规划
  planMultiPointRoute(
    waypoints: LatLng[],
    options?: RouteOptions
  ): Promise<RouteResult>;

  // 在地图上显示路径
  displayRoute(route: RouteResult, options?: RouteDisplayOptions): void;

  // 清除路径显示
  clearRoute(): void;

  // 清除所有路径
  clearAllRoutes(): void;
}

// 路径规划事件类型
export interface RoutingEvents {
  'route-start': (origin: LatLng, destination: LatLng) => void;
  'route-complete': (result: RouteResult) => void;
  'route-error': (error: Error) => void;
  'route-display': (route: RouteResult) => void;
  'route-clear': () => void;
}
