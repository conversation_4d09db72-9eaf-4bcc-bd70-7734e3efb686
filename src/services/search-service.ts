// 华为地图搜索服务实现

import type {
  LatLng,
  SearchOptions,
  SearchResult,
  GeocodingResult,
  ReverseGeocodingResult,
  POIResult,
  SuggestionResult,
  SearchService,
  AddressComponents
} from '../types/search';

/**
 * 华为地图搜索服务
 */
export class HuaweiSearchService implements SearchService {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://mapapi.cloud.huawei.com';
  }

  /**
   * 地点搜索
   */
  async searchPlace(query: string, options: SearchOptions = {}): Promise<SearchResult[]> {
    try {
      const params = new URLSearchParams({
        key: this.apiKey,
        query: query,
        language: 'zh-CN'
      });

      // 添加可选参数
      if (options.center) {
        params.append('location', `${options.center.lat},${options.center.lng}`);
      }
      if (options.radius) {
        params.append('radius', options.radius.toString());
      }
      if (options.city) {
        params.append('city', options.city);
      }
      if (options.limit) {
        params.append('pagesize', Math.min(options.limit, 20).toString());
      }
      if (options.types && options.types.length > 0) {
        params.append('poitype', options.types.join('|'));
      }

      const response = await fetch(`${this.baseUrl}/mapApi/v1/siteService/searchByText?${params}`);
      
      if (!response.ok) {
        throw new Error(`搜索请求失败: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.returnCode !== '0') {
        throw new Error(`搜索失败: ${data.returnDesc}`);
      }

      return this.parseSearchResults(data.sites || []);
    } catch (error) {
      console.error('地点搜索失败:', error);
      throw error;
    }
  }

  /**
   * 地理编码 - 地址转坐标
   */
  async geocode(address: string): Promise<GeocodingResult> {
    try {
      const params = new URLSearchParams({
        key: this.apiKey,
        address: address,
        language: 'zh-CN'
      });

      const response = await fetch(`${this.baseUrl}/mapApi/v1/siteService/geocode?${params}`);
      
      if (!response.ok) {
        throw new Error(`地理编码请求失败: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.returnCode !== '0') {
        throw new Error(`地理编码失败: ${data.returnDesc}`);
      }

      const site = data.sites?.[0];
      if (!site) {
        throw new Error('未找到匹配的地址');
      }

      return {
        address: address,
        location: {
          lat: parseFloat(site.location.lat),
          lng: parseFloat(site.location.lng)
        },
        confidence: site.confidence || 1.0,
        components: this.parseAddressComponents(site)
      };
    } catch (error) {
      console.error('地理编码失败:', error);
      throw error;
    }
  }

  /**
   * 逆地理编码 - 坐标转地址
   */
  async reverseGeocode(lat: number, lng: number): Promise<ReverseGeocodingResult> {
    try {
      const params = new URLSearchParams({
        key: this.apiKey,
        location: `${lat},${lng}`,
        language: 'zh-CN',
        returnPoi: 'true'
      });

      const response = await fetch(`${this.baseUrl}/mapApi/v1/siteService/reverseGeocode?${params}`);
      
      if (!response.ok) {
        throw new Error(`逆地理编码请求失败: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.returnCode !== '0') {
        throw new Error(`逆地理编码失败: ${data.returnDesc}`);
      }

      const site = data.sites?.[0];
      if (!site) {
        throw new Error('未找到地址信息');
      }

      return {
        location: { lat, lng },
        formattedAddress: site.formatAddress || '',
        components: this.parseAddressComponents(site),
        nearbyPOIs: this.parsePOIResults(data.pois || [])
      };
    } catch (error) {
      console.error('逆地理编码失败:', error);
      throw error;
    }
  }

  /**
   * 周边搜索
   */
  async searchNearby(center: LatLng, radius: number, keyword?: string): Promise<POIResult[]> {
    try {
      const params = new URLSearchParams({
        key: this.apiKey,
        location: `${center.lat},${center.lng}`,
        radius: radius.toString(),
        language: 'zh-CN',
        pagesize: '20'
      });

      if (keyword) {
        params.append('query', keyword);
      }

      const response = await fetch(`${this.baseUrl}/mapApi/v1/siteService/nearbySearch?${params}`);
      
      if (!response.ok) {
        throw new Error(`周边搜索请求失败: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.returnCode !== '0') {
        throw new Error(`周边搜索失败: ${data.returnDesc}`);
      }

      return this.parsePOIResults(data.sites || []);
    } catch (error) {
      console.error('周边搜索失败:', error);
      throw error;
    }
  }

  /**
   * 搜索建议
   */
  async getSuggestions(query: string): Promise<SuggestionResult[]> {
    try {
      const params = new URLSearchParams({
        key: this.apiKey,
        query: query,
        language: 'zh-CN',
        pagesize: '10'
      });

      const response = await fetch(`${this.baseUrl}/mapApi/v1/siteService/querySuggestion?${params}`);
      
      if (!response.ok) {
        throw new Error(`搜索建议请求失败: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.returnCode !== '0') {
        throw new Error(`搜索建议失败: ${data.returnDesc}`);
      }

      return this.parseSuggestionResults(data.sites || []);
    } catch (error) {
      console.error('搜索建议失败:', error);
      throw error;
    }
  }

  /**
   * 解析搜索结果
   */
  private parseSearchResults(sites: any[]): SearchResult[] {
    return sites.map(site => ({
      id: site.siteId || site.id || Math.random().toString(36),
      name: site.name || '',
      address: site.formatAddress || site.address || '',
      location: {
        lat: parseFloat(site.location.lat),
        lng: parseFloat(site.location.lng)
      },
      type: site.poi?.poiType || 'unknown',
      distance: site.distance ? parseFloat(site.distance) : undefined,
      rating: site.poi?.rating ? parseFloat(site.poi.rating) : undefined,
      phone: site.poi?.phone || undefined,
      details: site.poi || {}
    }));
  }

  /**
   * 解析POI结果
   */
  private parsePOIResults(sites: any[]): POIResult[] {
    return sites.map(site => ({
      id: site.siteId || site.id || Math.random().toString(36),
      name: site.name || '',
      type: site.poi?.poiType || 'unknown',
      location: {
        lat: parseFloat(site.location.lat),
        lng: parseFloat(site.location.lng)
      },
      address: site.formatAddress || site.address || '',
      distance: site.distance ? parseFloat(site.distance) : 0,
      tags: site.poi?.tags || []
    }));
  }

  /**
   * 解析搜索建议结果
   */
  private parseSuggestionResults(sites: any[]): SuggestionResult[] {
    return sites.map(site => ({
      text: site.name || site.formatAddress || '',
      type: site.poi ? 'poi' : 'address',
      location: site.location ? {
        lat: parseFloat(site.location.lat),
        lng: parseFloat(site.location.lng)
      } : undefined,
      address: site.formatAddress || undefined
    }));
  }

  /**
   * 解析地址组件
   */
  private parseAddressComponents(site: any): AddressComponents {
    const addressDetail = site.addressDetail || {};
    return {
      country: addressDetail.country || '中国',
      province: addressDetail.province || '',
      city: addressDetail.city || '',
      district: addressDetail.district || '',
      street: addressDetail.street || '',
      streetNumber: addressDetail.streetNumber || '',
      postalCode: addressDetail.postalCode || ''
    };
  }
}
